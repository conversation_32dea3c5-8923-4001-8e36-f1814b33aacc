/* Base styles */
:root {
  --primary-color: #3a3a3a;
  --accent-color: #d4af37;
  --bg-color: #f9f9f9;
  --text-color: #333;
  --light-color: #fff;
  --border-color: #eaeaea;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--bg-color);
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

h1, h2, h3 {
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  line-height: 1.2;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 8px;
}

a {
  text-decoration: none;
  color: var(--accent-color);
  transition: color 0.3s ease;
}

a:hover {
  color: #b08e2c;
}

.btn {
  display: inline-block;
  background-color: var(--accent-color);
  color: var(--light-color);
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.btn:hover {
  background-color: #b08e2c;
  color: var(--light-color);
}

/* Header */
header {
  background-color: var(--light-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
}

.logo {
  font-size: 1.8rem;
  color: var(--primary-color);
}

nav ul {
  display: flex;
  list-style: none;
}

nav li {
  margin-left: 30px;
}

nav a {
  color: var(--primary-color);
  font-weight: 500;
  position: relative;
}

nav a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--accent-color);
  transition: width 0.3s ease;
}

nav a:hover::after,
nav a.active::after {
  width: 100%;
}



/* Hero section */
.hero {
  background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('https://images.unsplash.com/photo-1517248135467-4c7edcad34c4') no-repeat center center/cover;
  color: var(--light-color);
  text-align: center;
  padding: 120px 0;
}

.hero h2 {
  font-size: 3rem;
  margin-bottom: 20px;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* About section */
.about {
  padding: 80px 0;
  background-color: var(--light-color);
}

.about-content {
  display: flex;
  align-items: center;
  gap: 40px;
}

.about-text {
  flex: 1;
}

.about-text h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: var(--primary-color);
}

.about-text p {
  margin-bottom: 20px;
}

.about-image {
  flex: 1;
}

/* About page unique styles */
.about-hero {
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1542528180-a1208c5169a5') no-repeat center center/cover;
}

.about-timeline {
  padding: 80px 0;
  background-color: var(--light-color);
}

.about-timeline h3 {
  text-align: center;
  font-size: 2.2rem;
  margin-bottom: 50px;
  color: var(--primary-color);
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::after {
  content: '';
  position: absolute;
  width: 4px;
  background-color: var(--accent-color);
  top: 0;
  bottom: 0;
  left: 50%;
  margin-left: -2px;
}

.timeline-item {
  padding: 10px 40px;
  position: relative;
  width: 50%;
  box-sizing: border-box;
}

.timeline-item:nth-child(odd) {
  left: 0;
}

.timeline-item:nth-child(even) {
  left: 50%;
}

.timeline-year {
  position: absolute;
  width: 80px;
  height: 80px;
  right: -40px;
  background-color: var(--accent-color);
  border-radius: 50%;
  top: 15px;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--light-color);
  font-weight: bold;
  font-size: 1.2rem;
}

.timeline-item:nth-child(even) .timeline-year {
  left: -40px;
}

.timeline-content {
  padding: 20px;
  background-color: var(--bg-color);
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.timeline-content h4 {
  margin-bottom: 10px;
  color: var(--primary-color);
}

/* About philosophy section */
.about-philosophy {
  padding: 80px 0;
  background-color: var(--light-color);
}

/* About page gallery styles */
.about-gallery {
  padding: 60px 0;
  background-color: var(--bg-color);
}

.about-gallery h3 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 30px;
  color: var(--primary-color);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  max-width: 900px;
  margin: 0 auto;
}

.gallery-grid img {
  width: 100%;
  height: 120px;
  object-fit: cover;
  border-radius: 6px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.gallery-grid img:hover {
  transform: scale(1.05);
}

/* Philosophy section adjustments - smaller images */
.philosophy-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  align-items: center;
}

.philosophy-image {
  flex: 0 0 250px;
  max-width: 250px;
}

.philosophy-image img {
  max-height: 200px;
  object-fit: cover;
}

.philosophy-content {
  flex: 1;
  min-width: 300px;
}

/* About team section */
.about-team {
  padding: 80px 0;
  background-color: var(--bg-color);
}

.about-team h3 {
  text-align: center;
  font-size: 2.2rem;
  margin-bottom: 40px;
  color: var(--primary-color);
}

/* Team member image size adjustments */
.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.team-member {
  text-align: center;
  padding: 15px;
  background-color: var(--light-color);
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.team-member img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  margin: 0 auto 15px;
  border: 2px solid var(--accent-color);
  display: block;
}

.team-member h4 {
  font-size: 1.2rem;
  margin-bottom: 5px;
  color: var(--primary-color);
}

.member-title {
  color: var(--accent-color);
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.member-bio {
  font-size: 0.85rem;
  line-height: 1.4;
}



/* Philosophy values styling */
.philosophy-values {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.value-item {
  background-color: var(--bg-color);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.value-item h4 {
  color: var(--accent-color);
  margin-bottom: 10px;
  font-size: 1.2rem;
}

/* Featured dishes */
.featured {
  padding: 80px 0;
  background-color: var(--bg-color);
  text-align: center;
}

.featured h2 {
  font-size: 2.5rem;
  margin-bottom: 40px;
  color: var(--primary-color);
}

.dishes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.dish {
  background-color: var(--light-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.dish:hover {
  transform: translateY(-10px);
}

.dish img {
  height: 200px;
  object-fit: cover;
  width: 100%;
  border-radius: 8px 8px 0 0;
}

.dish h3 {
  margin: 20px 0 10px;
  color: var(--primary-color);
}

.dish p {
  margin-bottom: 20px;
  padding: 0 20px;
}

/* Testimonials section */
.testimonials {
  padding: 80px 0;
  background-color: var(--bg-color);
  text-align: center;
}

.testimonials h2 {
  font-size: 2.5rem;
  margin-bottom: 40px;
  color: var(--primary-color);
}

.testimonial {
  background-color: var(--light-color);
  padding: 30px;
  margin: 20px auto;
  max-width: 600px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.testimonial p {
  font-style: italic;
  margin-bottom: 15px;
}

.author {
  font-weight: 500;
  color: var(--accent-color);
}

/* Contact section */
.contact {
  padding: 80px 0;
  background-color: var(--light-color);
  text-align: center;
}

.contact h2 {
  font-size: 2.5rem;
  margin-bottom: 40px;
  color: var(--primary-color);
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.contact-info h3 {
  margin-bottom: 15px;
  color: var(--accent-color);
}

/* Footer */
footer {
  background-color: var(--primary-color);
  color: var(--light-color);
  padding: 30px 0;
  text-align: center;
}

/* Menu page styles - enhanced with images */
.page-title {
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5') no-repeat center center/cover;
  color: var(--light-color);
  text-align: center;
  padding: 80px 0;
}

.page-title h2 {
  font-size: 3rem;
  margin-bottom: 20px;
}

.menu-section {
  padding: 60px 0;
  background-color: var(--light-color);
}

.menu-section:nth-child(odd) {
  background-color: var(--bg-color);
}

.menu-section h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 40px;
  color: var(--primary-color);
  font-family: 'Playfair Display', serif;
}

.menu-items {
  max-width: 900px;
  margin: 0 auto;
}

.menu-item {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid var(--border-color);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item-image {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
  margin-right: 20px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.menu-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0;
}

.menu-item-info {
  flex: 1;
}

.menu-item-info h3 {
  margin-bottom: 8px;
  color: var(--primary-color);
  font-size: 1.3rem;
}

.menu-item-info p {
  color: var(--text-color);
  font-size: 0.95rem;
  line-height: 1.5;
}

.price {
  font-weight: 700;
  color: var(--accent-color);
  font-size: 1.2rem;
  margin-left: 15px;
  flex-shrink: 0;
}

/* Responsive adjustments for menu */
@media (max-width: 768px) {
  .menu-item {
    flex-direction: column;
    align-items: flex-start;
    text-align: center;
  }

  .menu-item-image {
    margin: 0 auto 15px;
    width: 150px;
    height: 150px;
  }

  .menu-item-info {
    text-align: center;
    margin-bottom: 15px;
  }

  .price {
    margin-left: 0;
  }
}



/* Responsive adjustments for unique pages */
@media (max-width: 768px) {
  .timeline::after {
    left: 31px;
  }

  .timeline-item {
    width: 100%;
    padding-left: 70px;
    padding-right: 25px;
  }

  .timeline-item:nth-child(even) {
    left: 0;
  }

  .timeline-year {
    left: -10px;
    right: auto;
    width: 60px;
    height: 60px;
    font-size: 1rem;
  }

  .timeline-item:nth-child(even) .timeline-year {
    left: -10px;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }
}
